// routes/auth.js - Rutas de Autenticación para La Legendaria Germayori
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// Validaciones
const registerValidation = [
  body('nombre')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('El nombre debe tener entre 2 y 50 caracteres'),
  body('apellido')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('El apellido debe tener entre 2 y 50 caracteres'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('telefono')
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Número de teléfono inválido'),
  body('pais')
    .trim()
    .isLength({ min: 2 })
    .withMessage('País requerido'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('La contraseña debe tener al menos 6 caracteres')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .notEmpty()
    .withMessage('Contraseña requerida')
];

// @route   POST /api/auth/register
// @desc    Registrar nuevo usuario
// @access  Public
router.post('/register', registerValidation, async (req, res) => {
  try {
    // Verificar errores de validación
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { nombre, apellido, email, telefono, pais, password } = req.body;

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Ya existe un usuario con este email'
      });
    }

    // Crear nuevo usuario
    const user = new User({
      nombre,
      apellido,
      email,
      telefono,
      pais,
      password,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    await user.save();

    // Generar token
    const token = user.generateAuthToken();

    // Actualizar estadísticas de login
    user.loginCount += 1;
    user.lastLogin = new Date();
    await user.save();

    res.status(201).json({
      success: true,
      message: '¡Bienvenido a La Legendaria Germayori! Usuario registrado exitosamente',
      data: {
        user: {
          id: user._id,
          nombre: user.nombre,
          apellido: user.apellido,
          nombreCompleto: user.nombreCompleto,
          email: user.email,
          telefono: user.telefono,
          pais: user.pais,
          subscriptionStatus: user.subscriptionStatus,
          totalAnalysis: user.totalAnalysis,
          analysisThisMonth: user.analysisThisMonth,
          isSubscriptionActive: user.isSubscriptionActive,
          createdAt: user.createdAt
        },
        token,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    console.error('Error en registro:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/auth/login
// @desc    Iniciar sesión
// @access  Public
router.post('/login', loginValidation, async (req, res) => {
  try {
    // Verificar errores de validación
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Buscar usuario por email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Credenciales inválidas'
      });
    }

    // Verificar si la cuenta está activa
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Cuenta desactivada. Contacta al soporte.'
      });
    }

    // Verificar contraseña
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Credenciales inválidas'
      });
    }

    // Generar token
    const token = user.generateAuthToken();

    // Actualizar estadísticas de login
    user.loginCount += 1;
    user.lastLogin = new Date();
    user.ipAddress = req.ip;
    user.userAgent = req.get('User-Agent');
    await user.save();

    res.json({
      success: true,
      message: `¡Bienvenido de vuelta, ${user.nombre}!`,
      data: {
        user: {
          id: user._id,
          nombre: user.nombre,
          apellido: user.apellido,
          nombreCompleto: user.nombreCompleto,
          email: user.email,
          telefono: user.telefono,
          pais: user.pais,
          subscriptionStatus: user.subscriptionStatus,
          totalAnalysis: user.totalAnalysis,
          analysisThisMonth: user.analysisThisMonth,
          isSubscriptionActive: user.isSubscriptionActive,
          lastLogin: user.lastLogin,
          loginCount: user.loginCount
        },
        token,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   GET /api/auth/me
// @desc    Obtener información del usuario actual
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          nombre: user.nombre,
          apellido: user.apellido,
          nombreCompleto: user.nombreCompleto,
          email: user.email,
          telefono: user.telefono,
          pais: user.pais,
          subscriptionStatus: user.subscriptionStatus,
          totalAnalysis: user.totalAnalysis,
          analysisThisMonth: user.analysisThisMonth,
          isSubscriptionActive: user.isSubscriptionActive,
          analysisLimit: user.getAnalysisLimit(),
          canMakeAnalysis: user.canMakeAnalysis(),
          preferences: user.preferences,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo perfil:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   PUT /api/auth/profile
// @desc    Actualizar perfil del usuario
// @access  Private
router.put('/profile', auth, [
  body('nombre').optional().trim().isLength({ min: 2, max: 50 }),
  body('apellido').optional().trim().isLength({ min: 2, max: 50 }),
  body('telefono').optional().matches(/^[\+]?[1-9][\d]{0,15}$/),
  body('pais').optional().trim().isLength({ min: 2 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    // Actualizar campos permitidos
    const allowedUpdates = ['nombre', 'apellido', 'telefono', 'pais'];
    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        user[field] = req.body[field];
      }
    });

    await user.save();

    res.json({
      success: true,
      message: 'Perfil actualizado exitosamente',
      data: {
        user: {
          id: user._id,
          nombre: user.nombre,
          apellido: user.apellido,
          nombreCompleto: user.nombreCompleto,
          email: user.email,
          telefono: user.telefono,
          pais: user.pais
        }
      }
    });

  } catch (error) {
    console.error('Error actualizando perfil:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/auth/change-password
// @desc    Cambiar contraseña
// @access  Private
router.post('/change-password', auth, [
  body('currentPassword').notEmpty().withMessage('Contraseña actual requerida'),
  body('newPassword').isLength({ min: 6 }).withMessage('La nueva contraseña debe tener al menos 6 caracteres')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    // Verificar contraseña actual
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Contraseña actual incorrecta'
      });
    }

    // Actualizar contraseña
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: 'Contraseña actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error cambiando contraseña:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Cerrar sesión (invalidar token)
// @access  Private
router.post('/logout', auth, async (req, res) => {
  try {
    // En una implementación real, aquí podrías agregar el token a una blacklist
    // Por ahora, simplemente confirmamos el logout
    
    res.json({
      success: true,
      message: 'Sesión cerrada exitosamente'
    });

  } catch (error) {
    console.error('Error en logout:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
});

module.exports = router;
