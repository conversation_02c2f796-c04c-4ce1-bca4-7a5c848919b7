// middleware/auth.js - Middleware de Autenticación para La Legendaria Germayori
const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware principal de autenticación
const auth = async (req, res, next) => {
  try {
    // Obtener token del header
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'Acceso denegado. Token no proporcionado.',
        code: 'NO_TOKEN'
      });
    }

    // Verificar formato del token (Bearer token)
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Acceso denegado. Formato de token inválido.',
        code: 'INVALID_TOKEN_FORMAT'
      });
    }

    // Verificar y decodificar el token
    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || 'la-legendaria-germayori-secret-key'
    );

    // Buscar el usuario en la base de datos
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Token inválido. Usuario no encontrado.',
        code: 'USER_NOT_FOUND'
      });
    }

    // Verificar si la cuenta está activa
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Cuenta desactivada. Contacta al soporte.',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Agregar información del usuario al request
    req.user = {
      userId: user._id,
      email: user.email,
      nombre: user.nombre,
      apellido: user.apellido,
      subscriptionStatus: user.subscriptionStatus,
      isSubscriptionActive: user.isSubscriptionActive,
      analysisLimit: user.getAnalysisLimit(),
      canMakeAnalysis: user.canMakeAnalysis()
    };

    next();

  } catch (error) {
    console.error('Error en middleware de autenticación:', error);

    // Manejar diferentes tipos de errores JWT
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token inválido.',
        code: 'INVALID_TOKEN'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expirado. Por favor, inicia sesión nuevamente.',
        code: 'TOKEN_EXPIRED'
      });
    }

    if (error.name === 'NotBeforeError') {
      return res.status(401).json({
        success: false,
        message: 'Token no válido aún.',
        code: 'TOKEN_NOT_ACTIVE'
      });
    }

    // Error genérico
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor en autenticación.',
      code: 'AUTH_ERROR'
    });
  }
};

// Middleware para verificar suscripción activa
const requireActiveSubscription = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Autenticación requerida.',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!req.user.isSubscriptionActive) {
      return res.status(403).json({
        success: false,
        message: 'Suscripción activa requerida para acceder a esta función.',
        code: 'SUBSCRIPTION_REQUIRED',
        data: {
          currentStatus: req.user.subscriptionStatus,
          upgradeUrl: '/api/payment/plans'
        }
      });
    }

    next();

  } catch (error) {
    console.error('Error en middleware de suscripción:', error);
    res.status(500).json({
      success: false,
      message: 'Error verificando suscripción.',
      code: 'SUBSCRIPTION_CHECK_ERROR'
    });
  }
};

// Middleware para verificar límite de análisis
const checkAnalysisLimit = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Autenticación requerida.',
        code: 'AUTH_REQUIRED'
      });
    }

    // Obtener usuario completo para verificar límites actualizados
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado.',
        code: 'USER_NOT_FOUND'
      });
    }

    // Verificar si puede hacer más análisis
    if (!user.canMakeAnalysis()) {
      const limit = user.getAnalysisLimit();
      
      return res.status(403).json({
        success: false,
        message: `Has alcanzado tu límite de ${limit} análisis este mes.`,
        code: 'ANALYSIS_LIMIT_REACHED',
        data: {
          currentCount: user.analysisThisMonth,
          limit: limit,
          subscriptionStatus: user.subscriptionStatus,
          upgradeUrl: '/api/payment/plans'
        }
      });
    }

    // Actualizar información del usuario en el request
    req.user.analysisThisMonth = user.analysisThisMonth;
    req.user.totalAnalysis = user.totalAnalysis;

    next();

  } catch (error) {
    console.error('Error verificando límite de análisis:', error);
    res.status(500).json({
      success: false,
      message: 'Error verificando límite de análisis.',
      code: 'ANALYSIS_LIMIT_CHECK_ERROR'
    });
  }
};

// Middleware para verificar roles específicos
const requireRole = (roles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Autenticación requerida.',
          code: 'AUTH_REQUIRED'
        });
      }

      // Convertir a array si es un string
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (!allowedRoles.includes(req.user.subscriptionStatus)) {
        return res.status(403).json({
          success: false,
          message: 'Permisos insuficientes para acceder a esta función.',
          code: 'INSUFFICIENT_PERMISSIONS',
          data: {
            required: allowedRoles,
            current: req.user.subscriptionStatus
          }
        });
      }

      next();

    } catch (error) {
      console.error('Error en middleware de roles:', error);
      res.status(500).json({
        success: false,
        message: 'Error verificando permisos.',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

// Middleware opcional de autenticación (no falla si no hay token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      req.user = null;
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || 'la-legendaria-germayori-secret-key'
    );

    const user = await User.findById(decoded.userId);
    
    if (user && user.isActive) {
      req.user = {
        userId: user._id,
        email: user.email,
        nombre: user.nombre,
        apellido: user.apellido,
        subscriptionStatus: user.subscriptionStatus,
        isSubscriptionActive: user.isSubscriptionActive
      };
    } else {
      req.user = null;
    }

    next();

  } catch (error) {
    // En caso de error, simplemente continuar sin usuario
    req.user = null;
    next();
  }
};

// Middleware para logging de requests autenticados
const logAuthenticatedRequest = (req, res, next) => {
  if (req.user) {
    console.log(`[${new Date().toISOString()}] Usuario autenticado: ${req.user.email} - ${req.method} ${req.originalUrl}`);
  }
  next();
};

module.exports = {
  auth,
  requireActiveSubscription,
  checkAnalysisLimit,
  requireRole,
  optionalAuth,
  logAuthenticatedRequest
};
