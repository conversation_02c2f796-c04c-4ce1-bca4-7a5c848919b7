// routes/payment.js - Rutas de Pago para La Legendaria Germayori
const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Payment = require('../models/Payment');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Planes disponibles
const PLANS = {
  premium_monthly: {
    name: 'Premium Mensual',
    description: 'Acceso completo a análisis con IA',
    price: 29.99,
    currency: 'USD',
    interval: 'month',
    analysisLimit: 100,
    features: [
      'Hasta 100 análisis por mes',
      'Análisis con IA avanzada',
      'Soporte por email',
      'Reportes detallados'
    ]
  },
  premium_yearly: {
    name: 'Premium Anual',
    description: 'Acceso completo con descuento anual',
    price: 299.99,
    currency: 'USD',
    interval: 'year',
    analysisLimit: 100,
    features: [
      'Hasta 100 análisis por mes',
      'Análisis con IA avanzada',
      'Soporte prioritario',
      'Reportes detallados',
      '2 meses gratis'
    ]
  },
  vip_monthly: {
    name: 'VIP Mensual',
    description: 'Acceso ilimitado y funciones exclusivas',
    price: 99.99,
    currency: 'USD',
    interval: 'month',
    analysisLimit: -1,
    features: [
      'Análisis ilimitados',
      'IA con estrategia Germayori exclusiva',
      'Soporte 24/7',
      'Reportes premium',
      'Señales en tiempo real',
      'Acceso a comunidad VIP'
    ]
  },
  vip_yearly: {
    name: 'VIP Anual',
    description: 'Plan VIP con máximo descuento',
    price: 999.99,
    currency: 'USD',
    interval: 'year',
    analysisLimit: -1,
    features: [
      'Análisis ilimitados',
      'IA con estrategia Germayori exclusiva',
      'Soporte 24/7',
      'Reportes premium',
      'Señales en tiempo real',
      'Acceso a comunidad VIP',
      '2 meses gratis'
    ]
  }
};

// @route   GET /api/payment/plans
// @desc    Obtener planes disponibles
// @access  Public
router.get('/plans', (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Planes de La Legendaria Germayori',
      data: {
        plans: PLANS,
        currency: 'USD',
        freePlan: {
          name: 'Plan Gratuito',
          description: 'Prueba nuestro servicio',
          price: 0,
          analysisLimit: 5,
          features: [
            'Hasta 5 análisis por mes',
            'Análisis básico con IA',
            'Soporte por email'
          ]
        }
      }
    });
  } catch (error) {
    console.error('Error obteniendo planes:', error);
    res.status(500).json({
      success: false,
      message: 'Error obteniendo planes',
      error: error.message
    });
  }
});

// @route   POST /api/payment/create-payment-intent
// @desc    Crear intención de pago con Stripe
// @access  Private
router.post('/create-payment-intent', auth, [
  body('planType').isIn(Object.keys(PLANS)).withMessage('Plan inválido'),
  body('billingInfo.email').isEmail().withMessage('Email inválido'),
  body('billingInfo.name').notEmpty().withMessage('Nombre requerido')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { planType, billingInfo } = req.body;
    const plan = PLANS[planType];

    if (!plan) {
      return res.status(400).json({
        success: false,
        message: 'Plan no encontrado'
      });
    }

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    // Crear o obtener customer de Stripe
    let stripeCustomer;
    if (user.stripeCustomerId) {
      stripeCustomer = await stripe.customers.retrieve(user.stripeCustomerId);
    } else {
      stripeCustomer = await stripe.customers.create({
        email: user.email,
        name: `${user.nombre} ${user.apellido}`,
        phone: user.telefono,
        metadata: {
          userId: user._id.toString(),
          country: user.pais
        }
      });
      
      user.stripeCustomerId = stripeCustomer.id;
      await user.save();
    }

    // Crear Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(plan.price * 100), // Convertir a centavos
      currency: plan.currency.toLowerCase(),
      customer: stripeCustomer.id,
      metadata: {
        userId: user._id.toString(),
        planType: planType,
        userEmail: user.email
      },
      description: `${plan.name} - La Legendaria Germayori`
    });

    // Crear registro de pago en la base de datos
    const payment = new Payment({
      userId: user._id,
      stripePaymentIntentId: paymentIntent.id,
      stripeCustomerId: stripeCustomer.id,
      planType: planType,
      planDetails: plan,
      amount: plan.price,
      currency: plan.currency,
      status: 'pending',
      billingInfo: billingInfo,
      metadata: {
        source: 'web',
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    });

    await payment.save();

    res.json({
      success: true,
      message: 'Intención de pago creada exitosamente',
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        paymentId: payment.paymentId,
        amount: plan.price,
        currency: plan.currency,
        plan: plan
      }
    });

  } catch (error) {
    console.error('Error creando payment intent:', error);
    res.status(500).json({
      success: false,
      message: 'Error procesando pago',
      error: error.message
    });
  }
});

// @route   POST /api/payment/confirm-payment
// @desc    Confirmar pago exitoso
// @access  Private
router.post('/confirm-payment', auth, [
  body('paymentIntentId').notEmpty().withMessage('Payment Intent ID requerido')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { paymentIntentId } = req.body;

    // Verificar el pago en Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        success: false,
        message: 'Pago no completado',
        status: paymentIntent.status
      });
    }

    // Buscar el pago en la base de datos
    const payment = await Payment.findOne({ stripePaymentIntentId: paymentIntentId });
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Registro de pago no encontrado'
      });
    }

    // Actualizar el pago
    await payment.markAsPaid({
      paymentIntentId: paymentIntent.id,
      chargeId: paymentIntent.latest_charge,
      transactionId: paymentIntent.id
    });

    // Actualizar la suscripción del usuario
    const user = await User.findById(payment.userId);
    if (user) {
      const plan = PLANS[payment.planType];
      const now = new Date();
      
      // Calcular fecha de expiración
      let endDate = new Date(now);
      if (plan.interval === 'month') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (plan.interval === 'year') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }

      // Actualizar usuario
      user.subscriptionStatus = payment.planType.includes('vip') ? 'vip' : 'premium';
      user.subscriptionStartDate = now;
      user.subscriptionEndDate = endDate;
      
      await user.save();

      // Resetear contador mensual si es necesario
      await user.resetMonthlyAnalysis();
    }

    res.json({
      success: true,
      message: '¡Pago confirmado! Bienvenido a La Legendaria Germayori Premium',
      data: {
        payment: {
          id: payment.paymentId,
          amount: payment.amount,
          currency: payment.currency,
          plan: payment.planDetails,
          paidAt: payment.paidAt
        },
        subscription: {
          status: user.subscriptionStatus,
          startDate: user.subscriptionStartDate,
          endDate: user.subscriptionEndDate,
          analysisLimit: user.getAnalysisLimit()
        }
      }
    });

  } catch (error) {
    console.error('Error confirmando pago:', error);
    res.status(500).json({
      success: false,
      message: 'Error confirmando pago',
      error: error.message
    });
  }
});

// @route   GET /api/payment/history
// @desc    Obtener historial de pagos del usuario
// @access  Private
router.get('/history', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const payments = await Payment.find({ userId: req.user.userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-stripeCustomerId -stripePaymentIntentId -billingInfo.address');

    const total = await Payment.countDocuments({ userId: req.user.userId });

    res.json({
      success: true,
      message: 'Historial de pagos obtenido exitosamente',
      data: {
        payments,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo historial:', error);
    res.status(500).json({
      success: false,
      message: 'Error obteniendo historial de pagos',
      error: error.message
    });
  }
});

// @route   GET /api/payment/subscription
// @desc    Obtener información de suscripción actual
// @access  Private
router.get('/subscription', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    const currentPayment = await Payment.findOne({
      userId: user._id,
      status: 'succeeded',
      'subscription.isRecurring': true,
      'subscription.currentPeriodEnd': { $gt: new Date() }
    }).sort({ createdAt: -1 });

    res.json({
      success: true,
      data: {
        subscription: {
          status: user.subscriptionStatus,
          isActive: user.isSubscriptionActive,
          startDate: user.subscriptionStartDate,
          endDate: user.subscriptionEndDate,
          daysRemaining: currentPayment ? currentPayment.daysRemaining : 0,
          analysisLimit: user.getAnalysisLimit(),
          analysisUsed: user.analysisThisMonth,
          canMakeAnalysis: user.canMakeAnalysis()
        },
        currentPlan: currentPayment ? currentPayment.planDetails : null,
        availableUpgrades: Object.keys(PLANS).filter(planKey => {
          const plan = PLANS[planKey];
          return plan.analysisLimit > user.getAnalysisLimit() || plan.analysisLimit === -1;
        })
      }
    });

  } catch (error) {
    console.error('Error obteniendo suscripción:', error);
    res.status(500).json({
      success: false,
      message: 'Error obteniendo información de suscripción',
      error: error.message
    });
  }
});

module.exports = router;
