# 🚀 La Legendaria Germayori

**Análisis Financiero Avanzado con Inteligencia Artificial**

Una aplicación completa para análisis técnico de mercados financieros utilizando OpenAI GPT-4 Vision, con sistema de registro de usuarios, pagos con Stripe, y análisis de gráficos en tiempo real.

## 📋 Características Principales

### 🔐 Sistema de Autenticación
- ✅ Registro de usuarios con MongoDB
- ✅ Login/Logout seguro con JWT
- ✅ Gestión de perfiles de usuario
- ✅ Validación de datos completa

### 💳 Sistema de Pagos
- ✅ Integración con Stripe
- ✅ Planes Premium y VIP
- ✅ Gestión de suscripciones
- ✅ Historial de pagos

### 🤖 Análisis con IA
- ✅ Análisis de 4 timeframes (D1, H4, H1, M5)
- ✅ Estrategia Germayori implementada
- ✅ Señales BUY/SELL/HOLD/WAIT
- ✅ Análisis técnico detallado
- ✅ Gestión de riesgo automática

### 📊 Dashboard Completo
- ✅ Historial de análisis
- ✅ Estadísticas de usuario
- ✅ Límites por suscripción
- ✅ Interfaz responsive

## 🛠️ Tecnologías Utilizadas

### Backend
- **Node.js** + **Express.js**
- **MongoDB** con **Mongoose**
- **OpenAI GPT-4 Vision API**
- **Stripe** para pagos
- **JWT** para autenticación
- **Multer** para subida de archivos
- **bcryptjs** para encriptación

### Frontend
- **React 18** + **Vite**
- **React Router** para navegación
- **Tailwind CSS** para estilos
- **React Hook Form** para formularios
- **Axios** para API calls
- **React Hot Toast** para notificaciones

## 📦 Instalación

### Prerrequisitos
- Node.js 18+ 
- npm 9+
- Cuenta de MongoDB Atlas
- API Key de OpenAI
- Cuenta de Stripe (opcional para pagos)

### 1. Clonar el Repositorio
```bash
git clone https://github.com/tu-usuario/la-legendaria-germayori.git
cd la-legendaria-germayori
```

### 2. Configurar Backend

```bash
cd backend
npm install
```

**Configurar variables de entorno:**
El archivo `.env` ya está configurado con:
- ✅ MongoDB Atlas conectado
- ✅ JWT Secret configurado
- ⚠️ **NECESITAS AGREGAR:**

```env
# OpenAI API (REQUERIDO)
OPENAI_API_KEY=sk-tu-openai-api-key-aqui

# Stripe (OPCIONAL - para pagos)
STRIPE_SECRET_KEY=sk_test_tu-stripe-secret-key-aqui
STRIPE_PUBLISHABLE_KEY=pk_test_tu-stripe-publishable-key-aqui
```

### 3. Configurar Frontend

```bash
cd ../frontend
npm install
```

## 🚀 Ejecutar la Aplicación

### Opción 1: Desarrollo (Recomendado)

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
```
Servidor corriendo en: http://localhost:5000

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```
Aplicación corriendo en: http://localhost:3000

### Opción 2: Producción

**Backend:**
```bash
cd backend
npm start
```

**Frontend:**
```bash
cd frontend
npm run build
npm run preview
```

## 📱 Uso de la Aplicación

### 1. Registro de Usuario
1. Ve a http://localhost:3000/register
2. Completa el formulario con:
   - Nombre y apellido
   - Email
   - Teléfono
   - País
   - Contraseña

### 2. Realizar Análisis
1. Inicia sesión
2. Ve al Dashboard
3. Sube 4 imágenes de gráficos (D1, H4, H1, M5)
4. Espera el análisis con IA (30-60 segundos)
5. Revisa los resultados detallados

### 3. Planes de Suscripción
- **Gratuito**: 5 análisis por mes
- **Premium**: 100 análisis por mes ($29.99/mes)
- **VIP**: Análisis ilimitados ($99.99/mes)

## 🔧 API Endpoints

### Autenticación
- `POST /api/auth/register` - Registrar usuario
- `POST /api/auth/login` - Iniciar sesión
- `GET /api/auth/me` - Obtener perfil
- `PUT /api/auth/profile` - Actualizar perfil

### Análisis
- `POST /api/analysis/upload` - Subir imágenes para análisis
- `GET /api/analysis/:id` - Obtener resultado de análisis
- `GET /api/analysis` - Historial de análisis

### Pagos
- `GET /api/payment/plans` - Obtener planes disponibles
- `POST /api/payment/create-payment-intent` - Crear pago
- `GET /api/payment/history` - Historial de pagos

## 🔒 Configuración de Seguridad

### Variables de Entorno Críticas
```env
JWT_SECRET=la-legendaria-germayori-super-secret-key-2024
MONGO_URI=mongodb+srv://jhon:<EMAIL>/...
OPENAI_API_KEY=sk-tu-key-aqui
```

### Límites de Seguridad
- Rate limiting: 100 requests/15min
- Archivos: Máximo 20MB por imagen
- JWT: Expira en 7 días
- Validación completa de datos

## 📊 Base de Datos

### Colecciones MongoDB
- **users**: Información de usuarios y suscripciones
- **analyses**: Resultados de análisis con IA
- **payments**: Historial de pagos y suscripciones

### Estructura de Usuario
```javascript
{
  nombre: "Juan",
  apellido: "Pérez", 
  email: "<EMAIL>",
  subscriptionStatus: "premium",
  totalAnalysis: 25,
  analysisThisMonth: 8
}
```

## 🤖 Estrategia Germayori

La IA utiliza una estrategia propietaria que analiza:
- **Tendencias multi-timeframe**
- **Niveles de soporte y resistencia**
- **Patrones de velas japonesas**
- **Confluencias técnicas**
- **Gestión de riesgo 1:2**

## 📧 Contacto y Soporte

- **Email**: <EMAIL>
- **Desarrollador**: La Legendaria Germayori Team

## 🚨 Problemas Comunes

### Error de Espacio en Disco
```bash
npm error nospc ENOSPC: no space left on device
```
**Solución**: Libera espacio en disco y ejecuta `npm install` nuevamente.

### Error de MongoDB
```bash
MongoDB error: MongoNetworkError
```
**Solución**: Verifica que la IP esté en la whitelist de MongoDB Atlas.

### Error de OpenAI
```bash
Error: Invalid API key
```
**Solución**: Configura tu `OPENAI_API_KEY` en el archivo `.env`.

## 📈 Próximas Funcionalidades

- [ ] Notificaciones push
- [ ] Análisis en tiempo real
- [ ] Comunidad de traders
- [ ] Backtesting automático
- [ ] App móvil

---

**¡La Legendaria Germayori está lista para revolucionar tu trading con IA! 🚀📈**
