import React, { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios'
import toast from 'react-hot-toast'

const AuthContext = createContext()

// Configurar axios
axios.defaults.baseURL = 'http://localhost:5000'

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [token, setToken] = useState(localStorage.getItem('token'))

  // Configurar interceptor de axios para incluir token
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      delete axios.defaults.headers.common['Authorization']
    }
  }, [token])

  // Verificar token al cargar la aplicación
  useEffect(() => {
    const checkAuth = async () => {
      if (token) {
        try {
          const response = await axios.get('/api/auth/me')
          if (response.data.success) {
            setUser(response.data.data.user)
          } else {
            // Token inválido
            logout()
          }
        } catch (error) {
          console.error('Error verificando autenticación:', error)
          logout()
        }
      }
      setLoading(false)
    }

    checkAuth()
  }, [token])

  // Función de registro
  const register = async (userData) => {
    try {
      setLoading(true)
      const response = await axios.post('/api/auth/register', userData)
      
      if (response.data.success) {
        const { user, token } = response.data.data
        
        // Guardar token y usuario
        localStorage.setItem('token', token)
        setToken(token)
        setUser(user)
        
        toast.success(response.data.message || '¡Registro exitoso!')
        return { success: true, user }
      } else {
        toast.error(response.data.message || 'Error en el registro')
        return { success: false, error: response.data.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error en el registro'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Función de login
  const login = async (credentials) => {
    try {
      setLoading(true)
      const response = await axios.post('/api/auth/login', credentials)
      
      if (response.data.success) {
        const { user, token } = response.data.data
        
        // Guardar token y usuario
        localStorage.setItem('token', token)
        setToken(token)
        setUser(user)
        
        toast.success(response.data.message || '¡Bienvenido!')
        return { success: true, user }
      } else {
        toast.error(response.data.message || 'Error en el login')
        return { success: false, error: response.data.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error en el login'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Función de logout
  const logout = async () => {
    try {
      // Intentar notificar al servidor (opcional)
      if (token) {
        await axios.post('/api/auth/logout')
      }
    } catch (error) {
      // Ignorar errores del logout del servidor
      console.error('Error en logout del servidor:', error)
    } finally {
      // Limpiar estado local
      localStorage.removeItem('token')
      setToken(null)
      setUser(null)
      delete axios.defaults.headers.common['Authorization']
      toast.success('Sesión cerrada correctamente')
    }
  }

  // Función para actualizar perfil
  const updateProfile = async (profileData) => {
    try {
      setLoading(true)
      const response = await axios.put('/api/auth/profile', profileData)
      
      if (response.data.success) {
        // Actualizar usuario en el estado
        setUser(prevUser => ({
          ...prevUser,
          ...response.data.data.user
        }))
        
        toast.success(response.data.message || 'Perfil actualizado')
        return { success: true, user: response.data.data.user }
      } else {
        toast.error(response.data.message || 'Error actualizando perfil')
        return { success: false, error: response.data.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error actualizando perfil'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Función para cambiar contraseña
  const changePassword = async (passwordData) => {
    try {
      setLoading(true)
      const response = await axios.post('/api/auth/change-password', passwordData)
      
      if (response.data.success) {
        toast.success(response.data.message || 'Contraseña actualizada')
        return { success: true }
      } else {
        toast.error(response.data.message || 'Error cambiando contraseña')
        return { success: false, error: response.data.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error cambiando contraseña'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  // Función para refrescar datos del usuario
  const refreshUser = async () => {
    try {
      const response = await axios.get('/api/auth/me')
      if (response.data.success) {
        setUser(response.data.data.user)
        return response.data.data.user
      }
    } catch (error) {
      console.error('Error refrescando usuario:', error)
    }
    return null
  }

  // Verificar si el usuario está autenticado
  const isAuthenticated = () => {
    return !!token && !!user
  }

  // Verificar si el usuario tiene suscripción activa
  const hasActiveSubscription = () => {
    return user?.isSubscriptionActive || false
  }

  // Obtener límite de análisis
  const getAnalysisLimit = () => {
    return user?.analysisLimit || 5
  }

  // Verificar si puede hacer más análisis
  const canMakeAnalysis = () => {
    return user?.canMakeAnalysis || false
  }

  // Obtener análisis restantes
  const getRemainingAnalysis = () => {
    if (!user) return 0
    const limit = getAnalysisLimit()
    if (limit === -1) return Infinity // Ilimitado
    return Math.max(0, limit - (user.analysisThisMonth || 0))
  }

  const value = {
    // Estado
    user,
    loading,
    token,
    
    // Funciones de autenticación
    register,
    login,
    logout,
    updateProfile,
    changePassword,
    refreshUser,
    
    // Funciones de verificación
    isAuthenticated,
    hasActiveSubscription,
    getAnalysisLimit,
    canMakeAnalysis,
    getRemainingAnalysis
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
