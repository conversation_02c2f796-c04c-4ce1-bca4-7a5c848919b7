@echo off
echo ========================================
echo    LA LEGENDARIA GERMAYORI - INSTALLER
echo ========================================
echo.
echo Instalando dependencias para La Legendaria Germayori...
echo.

REM Verificar si Node.js está instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js no está instalado.
    echo Por favor instala Node.js desde https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detectado
node --version

REM Verificar si npm está instalado
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm no está instalado.
    pause
    exit /b 1
)

echo ✅ npm detectado
npm --version
echo.

REM Instalar dependencias del backend
echo 📦 Instalando dependencias del backend...
cd backend
if %errorlevel% neq 0 (
    echo ERROR: No se pudo acceder a la carpeta backend
    pause
    exit /b 1
)

npm install
if %errorlevel% neq 0 (
    echo ERROR: Falló la instalación del backend
    echo Posibles soluciones:
    echo 1. Libera espacio en disco
    echo 2. Ejecuta como administrador
    echo 3. Limpia cache: npm cache clean --force
    pause
    exit /b 1
)

echo ✅ Backend instalado correctamente
echo.

REM Volver al directorio raíz
cd ..

REM Instalar dependencias del frontend
echo 📦 Instalando dependencias del frontend...
cd frontend
if %errorlevel% neq 0 (
    echo ERROR: No se pudo acceder a la carpeta frontend
    pause
    exit /b 1
)

npm install
if %errorlevel% neq 0 (
    echo ERROR: Falló la instalación del frontend
    echo Posibles soluciones:
    echo 1. Libera espacio en disco
    echo 2. Ejecuta como administrador
    echo 3. Limpia cache: npm cache clean --force
    pause
    exit /b 1
)

echo ✅ Frontend instalado correctamente
echo.

REM Volver al directorio raíz
cd ..

echo ========================================
echo    ✅ INSTALACIÓN COMPLETADA
echo ========================================
echo.
echo La Legendaria Germayori está lista para usar!
echo.
echo PRÓXIMOS PASOS:
echo.
echo 1. Configura tu API key de OpenAI en backend/.env:
echo    OPENAI_API_KEY=sk-tu-api-key-aqui
echo.
echo 2. (Opcional) Configura Stripe para pagos en backend/.env:
echo    STRIPE_SECRET_KEY=sk_test_tu-key-aqui
echo.
echo 3. Ejecuta la aplicación:
echo    - Backend: cd backend ^&^& npm run dev
echo    - Frontend: cd frontend ^&^& npm run dev
echo.
echo 4. Abre http://localhost:3000 en tu navegador
echo.
echo ¡Disfruta analizando con IA! 🚀📈
echo.
pause
