// models/Analysis.js - Modelo de Análisis para La Legendaria Germayori
const mongoose = require('mongoose');

const analysisSchema = new mongoose.Schema({
  // Referencia al usuario
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'El ID del usuario es requerido']
  },
  
  // Información del análisis
  analysisId: {
    type: String,
    unique: true,
    required: true
  },
  
  // Imágenes subidas
  images: {
    d1: {
      filename: String,
      path: String,
      originalName: String,
      size: Number,
      uploadedAt: { type: Date, default: Date.now }
    },
    h4: {
      filename: String,
      path: String,
      originalName: String,
      size: Number,
      uploadedAt: { type: Date, default: Date.now }
    },
    h1: {
      filename: String,
      path: String,
      originalName: String,
      size: Number,
      uploadedAt: { type: Date, default: Date.now }
    },
    m5: {
      filename: String,
      path: String,
      originalName: String,
      size: Number,
      uploadedAt: { type: Date, default: Date.now }
    }
  },
  
  // Resultado del análisis de OpenAI
  aiAnalysis: {
    // Análisis técnico
    technicalAnalysis: {
      trend: {
        type: String,
        enum: ['bullish', 'bearish', 'neutral', 'consolidation']
      },
      strength: {
        type: Number,
        min: 1,
        max: 10
      },
      timeframe: String,
      keyLevels: {
        support: [Number],
        resistance: [Number]
      }
    },
    
    // Señal de trading
    signal: {
      action: {
        type: String,
        enum: ['BUY', 'SELL', 'HOLD', 'WAIT'],
        required: true
      },
      confidence: {
        type: Number,
        min: 0,
        max: 100,
        required: true
      },
      entryPrice: Number,
      stopLoss: Number,
      takeProfit: [Number], // Múltiples TPs
      riskReward: Number
    },
    
    // Análisis detallado
    detailedAnalysis: {
      marketStructure: String,
      priceAction: String,
      volumeAnalysis: String,
      indicators: String,
      riskAssessment: String,
      timeframeAlignment: String
    },
    
    // Estrategia Germayori específica
    germayoriStrategy: {
      patternDetected: String,
      confluences: [String],
      marketPhase: String,
      recommendation: String,
      notes: String
    }
  },
  
  // Metadata del análisis
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  
  processingTime: Number, // en milisegundos
  
  // Información adicional
  pair: String, // Par de divisas analizado (ej: EURUSD)
  market: String, // Mercado (forex, crypto, stocks)
  
  // Feedback del usuario
  userFeedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    wasAccurate: Boolean,
    submittedAt: Date
  },
  
  // Resultados reales (para tracking de precisión)
  actualResult: {
    outcome: {
      type: String,
      enum: ['profit', 'loss', 'breakeven', 'pending']
    },
    pips: Number,
    percentage: Number,
    closedAt: Date,
    notes: String
  },
  
  // Configuración del análisis
  analysisConfig: {
    includeVolume: { type: Boolean, default: true },
    includeFundamentals: { type: Boolean, default: false },
    riskLevel: {
      type: String,
      enum: ['conservative', 'moderate', 'aggressive'],
      default: 'moderate'
    },
    timeHorizon: {
      type: String,
      enum: ['scalping', 'day', 'swing', 'position'],
      default: 'day'
    }
  },
  
  // Datos de OpenAI
  openaiData: {
    model: String,
    tokensUsed: Number,
    cost: Number,
    requestId: String,
    responseTime: Number
  },
  
  // Archivos generados
  generatedFiles: {
    reportPdf: String,
    chartAnnotated: String,
    summary: String
  },
  
  // Flags
  isPublic: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  isArchived: { type: Boolean, default: false },
  
  // Estadísticas
  views: { type: Number, default: 0 },
  shares: { type: Number, default: 0 },
  
  // Errores
  errors: [{
    message: String,
    code: String,
    timestamp: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices para optimizar consultas
analysisSchema.index({ userId: 1, createdAt: -1 });
analysisSchema.index({ analysisId: 1 });
analysisSchema.index({ status: 1 });
analysisSchema.index({ 'aiAnalysis.signal.action': 1 });
analysisSchema.index({ pair: 1 });
analysisSchema.index({ createdAt: -1 });

// Virtual para calcular accuracy
analysisSchema.virtual('accuracy').get(function() {
  if (!this.actualResult.outcome || this.actualResult.outcome === 'pending') {
    return null;
  }
  
  const signal = this.aiAnalysis.signal.action;
  const outcome = this.actualResult.outcome;
  
  if (signal === 'BUY' || signal === 'SELL') {
    return outcome === 'profit' ? 100 : 0;
  }
  
  return null;
});

// Método para generar ID único de análisis
analysisSchema.pre('save', function(next) {
  if (!this.analysisId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.analysisId = `LG-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Método para incrementar vistas
analysisSchema.methods.incrementViews = async function() {
  this.views += 1;
  return await this.save();
};

// Método para calcular tiempo de procesamiento
analysisSchema.methods.setProcessingTime = function(startTime) {
  this.processingTime = Date.now() - startTime;
};

// Método para marcar como completado
analysisSchema.methods.markCompleted = async function(aiResult) {
  this.status = 'completed';
  this.aiAnalysis = aiResult;
  return await this.save();
};

// Método para marcar como fallido
analysisSchema.methods.markFailed = async function(error) {
  this.status = 'failed';
  this.errors.push({
    message: error.message,
    code: error.code || 'UNKNOWN_ERROR'
  });
  return await this.save();
};

// Método estático para obtener estadísticas del usuario
analysisSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalAnalysis: { $sum: 1 },
        completedAnalysis: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        avgConfidence: { $avg: '$aiAnalysis.signal.confidence' },
        buySignals: {
          $sum: { $cond: [{ $eq: ['$aiAnalysis.signal.action', 'BUY'] }, 1, 0] }
        },
        sellSignals: {
          $sum: { $cond: [{ $eq: ['$aiAnalysis.signal.action', 'SELL'] }, 1, 0] }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalAnalysis: 0,
    completedAnalysis: 0,
    avgConfidence: 0,
    buySignals: 0,
    sellSignals: 0
  };
};

module.exports = mongoose.model('Analysis', analysisSchema);
