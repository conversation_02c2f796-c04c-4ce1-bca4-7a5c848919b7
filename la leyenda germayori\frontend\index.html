<!doctype html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="La Legendaria Germayori - Análisis financiero avanzado con Inteligencia Artificial. Obtén señales de trading precisas y análisis técnico profesional." />
    <meta name="keywords" content="trading, análisis técnico, IA, forex, criptomonedas, señales de trading, Germayori" />
    <meta name="author" content="La Legendaria Germayori" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://la-legendaria-germayori.com/" />
    <meta property="og:title" content="La Legendaria Germayori - Análisis Financiero con IA" />
    <meta property="og:description" content="Análisis financiero avanzado con Inteligencia Artificial. Obtén señales de trading precisas y análisis técnico profesional." />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://la-legendaria-germayori.com/" />
    <meta property="twitter:title" content="La Legendaria Germayori - Análisis Financiero con IA" />
    <meta property="twitter:description" content="Análisis financiero avanzado con Inteligencia Artificial. Obtén señales de trading precisas y análisis técnico profesional." />
    <meta property="twitter:image" content="/og-image.jpg" />

    <title>La Legendaria Germayori - Análisis Financiero con IA</title>
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-logo {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #ffd700;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 1rem;
        font-size: 1.1rem;
        opacity: 0.9;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">La Legendaria Germayori</div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Cargando análisis financiero con IA...</div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.jsx"></script>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
