{"name": "la-legendaria-germayori-backend", "version": "1.0.0", "description": "Backend API para La Legendaria Germayori - Análisis financiero con IA", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["trading", "financial-analysis", "ai", "openai", "forex", "ger<PERSON><PERSON><PERSON>", "technical-analysis"], "author": "La Legendaria Germayori Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "openai": "^4.20.1", "stripe": "^14.9.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "@types/node": "^20.10.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/germayori/la-legendaria-germayori.git"}, "bugs": {"url": "https://github.com/germayori/la-legendaria-germayori/issues", "email": "<EMAIL>"}, "homepage": "https://la-legendaria-germayori.com"}