# La Legendaria Germayori - Variables de Entorno
# Copia este archivo como .env y configura tus valores

# Configuración del Servidor
NODE_ENV=development
PORT=5000

# Base de Datos MongoDB
MONGO_URI=mongodb://localhost:27017/la-legendaria-germayori
# Para MongoDB Atlas:
# MONGO_URI=mongodb+srv://username:<EMAIL>/la-legendaria-germayori

# Autenticación JWT
JWT_SECRET=tu-jwt-secret-super-seguro-aqui
JWT_EXPIRES_IN=7d

# OpenAI API
OPENAI_API_KEY=sk-tu-openai-api-key-aqui
OPENAI_MODEL=gpt-4-vision-preview

# Stripe (Pagos)
STRIPE_SECRET_KEY=sk_test_tu-stripe-secret-key-aqui
STRIPE_PUBLISHABLE_KEY=pk_test_tu-stripe-publishable-key-aqui
STRIPE_WEBHOOK_SECRET=whsec_tu-stripe-webhook-secret-aqui

# Email (para notificaciones)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=tu-app-password-aqui
EMAIL_FROM=<EMAIL>

# URLs del Frontend
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001

# Configuración de Archivos
MAX_FILE_SIZE=20971520
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuración de Logs
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Configuración de Redis (para cache y sesiones)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Configuración de AWS S3 (opcional para almacenamiento de archivos)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=la-legendaria-germayori-files

# Configuración de Análisis
DEFAULT_ANALYSIS_TIMEOUT=60000
MAX_CONCURRENT_ANALYSIS=5

# Configuración de Notificaciones Push
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=

# URLs de Webhooks
WEBHOOK_URL_PAYMENT_SUCCESS=
WEBHOOK_URL_ANALYSIS_COMPLETE=

# Configuración de Monitoreo
SENTRY_DSN=
ANALYTICS_ID=

# Configuración de Desarrollo
DEBUG=true
VERBOSE_LOGGING=true
