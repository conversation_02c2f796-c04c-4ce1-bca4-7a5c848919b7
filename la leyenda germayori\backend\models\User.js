// models/User.js - Modelo de Usuario para La Legendaria Germayori
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // Información personal
  nombre: {
    type: String,
    required: [true, 'El nombre es requerido'],
    trim: true,
    minlength: [2, 'El nombre debe tener al menos 2 caracteres'],
    maxlength: [50, 'El nombre no puede exceder 50 caracteres']
  },
  apellido: {
    type: String,
    required: [true, 'El apellido es requerido'],
    trim: true,
    minlength: [2, 'El apellido debe tener al menos 2 caracteres'],
    maxlength: [50, 'El apellido no puede exceder 50 caracteres']
  },
  email: {
    type: String,
    required: [true, 'El email es requerido'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email inválido']
  },
  telefono: {
    type: String,
    required: [true, 'El teléfono es requerido'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Número de teléfono inválido']
  },
  pais: {
    type: String,
    required: [true, 'El país es requerido'],
    trim: true
  },
  
  // Autenticación
  password: {
    type: String,
    required: [true, 'La contraseña es requerida'],
    minlength: [6, 'La contraseña debe tener al menos 6 caracteres']
  },
  
  // Estado de la cuenta
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // Suscripción y pagos
  subscriptionStatus: {
    type: String,
    enum: ['free', 'premium', 'vip'],
    default: 'free'
  },
  subscriptionStartDate: Date,
  subscriptionEndDate: Date,
  stripeCustomerId: String,
  stripeSubscriptionId: String,
  
  // Estadísticas de uso
  totalAnalysis: {
    type: Number,
    default: 0
  },
  analysisThisMonth: {
    type: Number,
    default: 0
  },
  lastAnalysisDate: Date,
  
  // Configuraciones de usuario
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    },
    language: { type: String, default: 'es' },
    timezone: { type: String, default: 'America/Panama' }
  },
  
  // Metadata
  lastLogin: Date,
  loginCount: {
    type: Number,
    default: 0
  },
  ipAddress: String,
  userAgent: String
}, {
  timestamps: true, // Agrega createdAt y updatedAt automáticamente
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices para optimizar consultas
userSchema.index({ email: 1 });
userSchema.index({ subscriptionStatus: 1 });
userSchema.index({ createdAt: -1 });

// Virtual para nombre completo
userSchema.virtual('nombreCompleto').get(function() {
  return `${this.nombre} ${this.apellido}`;
});

// Virtual para verificar si la suscripción está activa
userSchema.virtual('isSubscriptionActive').get(function() {
  if (this.subscriptionStatus === 'free') return true;
  return this.subscriptionEndDate && this.subscriptionEndDate > new Date();
});

// Middleware pre-save para hashear la contraseña
userSchema.pre('save', async function(next) {
  // Solo hashear la contraseña si ha sido modificada
  if (!this.isModified('password')) return next();
  
  try {
    // Hashear la contraseña con salt de 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Método para comparar contraseñas
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Método para generar token JWT
userSchema.methods.generateAuthToken = function() {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { 
      userId: this._id,
      email: this.email,
      subscriptionStatus: this.subscriptionStatus
    },
    process.env.JWT_SECRET || 'la-legendaria-germayori-secret-key',
    { expiresIn: '7d' }
  );
};

// Método para incrementar contador de análisis
userSchema.methods.incrementAnalysisCount = async function() {
  this.totalAnalysis += 1;
  this.analysisThisMonth += 1;
  this.lastAnalysisDate = new Date();
  return await this.save();
};

// Método para resetear contador mensual
userSchema.methods.resetMonthlyAnalysis = async function() {
  this.analysisThisMonth = 0;
  return await this.save();
};

// Método para obtener límite de análisis según suscripción
userSchema.methods.getAnalysisLimit = function() {
  switch (this.subscriptionStatus) {
    case 'free':
      return 5; // 5 análisis gratis por mes
    case 'premium':
      return 100; // 100 análisis por mes
    case 'vip':
      return -1; // Ilimitado
    default:
      return 5;
  }
};

// Método para verificar si puede hacer más análisis
userSchema.methods.canMakeAnalysis = function() {
  const limit = this.getAnalysisLimit();
  if (limit === -1) return true; // Ilimitado
  return this.analysisThisMonth < limit;
};

// Método para limpiar datos sensibles antes de enviar al frontend
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.emailVerificationToken;
  delete user.passwordResetToken;
  delete user.passwordResetExpires;
  delete user.stripeCustomerId;
  delete user.stripeSubscriptionId;
  return user;
};

module.exports = mongoose.model('User', userSchema);
