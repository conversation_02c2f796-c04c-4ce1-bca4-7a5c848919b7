// app.js
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
app.use(cors());
app.use(express.json());

// Crear carpeta 'uploads' si no existe
const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}

// Static folder for uploaded images
app.use('/uploads', express.static(uploadDir));

// MongoDB connection
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => console.log('MongoDB connected'))
  .catch(err => console.log('MongoDB error:', err));

// Multer config for image uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});
const upload = multer({ storage: storage });

// ROUTE: Upload images (D1, H4, H1, M5)
app.post('/api/upload', upload.array('charts', 4), async (req, res) => {
  try {
    if (!req.files || req.files.length !== 4) {
      return res.status(400).json({ error: 'Debes subir exactamente 4 imágenes (D1, H4, H1, M5)' });
    }

    const imagePaths = req.files.map(file => file.path);

    // Aquí puedes llamar al servicio de OpenAI para análisis
    // const result = await analyzeImagesWithOpenAI(imagePaths);

    res.json({
      message: 'Imágenes recibidas correctamente',
      images: imagePaths,
      // signal: result
    });
  } catch (err) {
    console.error('Error en upload:', err);
    res.status(500).json({ error: 'Error al procesar las imágenes' });
  }
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Servidor \"La Leyenda Germayori\" corriendo en puerto ${PORT}`));
