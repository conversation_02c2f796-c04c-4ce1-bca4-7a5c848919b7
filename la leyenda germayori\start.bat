@echo off
echo ========================================
echo    LA LEGENDARIA GERMAYORI - STARTER
echo ========================================
echo.
echo Iniciando La Legendaria Germayori...
echo.

REM Verificar si las dependencias están instaladas
if not exist "backend\node_modules" (
    echo ERROR: Dependencias del backend no instaladas
    echo Ejecuta install.bat primero
    pause
    exit /b 1
)

if not exist "frontend\node_modules" (
    echo ERROR: Dependencias del frontend no instaladas
    echo Ejecuta install.bat primero
    pause
    exit /b 1
)

REM Verificar archivo .env
if not exist "backend\.env" (
    echo WARNING: Archivo .env no encontrado en backend
    echo Copiando .env.example...
    copy "backend\.env.example" "backend\.env"
    echo.
    echo IMPORTANTE: Configura tu OPENAI_API_KEY en backend\.env
    echo.
)

echo ✅ Verificaciones completadas
echo.

REM Crear archivo batch temporal para ejecutar ambos servidores
echo @echo off > temp_start_backend.bat
echo cd backend >> temp_start_backend.bat
echo echo 🚀 Iniciando Backend en puerto 5000... >> temp_start_backend.bat
echo npm run dev >> temp_start_backend.bat

echo @echo off > temp_start_frontend.bat
echo timeout /t 3 /nobreak ^>nul >> temp_start_frontend.bat
echo cd frontend >> temp_start_frontend.bat
echo echo 🌐 Iniciando Frontend en puerto 3000... >> temp_start_frontend.bat
echo npm run dev >> temp_start_frontend.bat

echo 🚀 Iniciando Backend (puerto 5000)...
start "La Legendaria Germayori - Backend" temp_start_backend.bat

echo 🌐 Iniciando Frontend (puerto 3000)...
start "La Legendaria Germayori - Frontend" temp_start_frontend.bat

echo.
echo ========================================
echo    ✅ SERVIDORES INICIADOS
echo ========================================
echo.
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo La aplicación se abrirá automáticamente en tu navegador...
echo.

REM Esperar un poco y abrir el navegador
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo Para detener los servidores, cierra las ventanas de terminal.
echo.
echo ¡Disfruta La Legendaria Germayori! 🚀📈
echo.

REM Limpiar archivos temporales después de un tiempo
timeout /t 10 /nobreak >nul
del temp_start_backend.bat 2>nul
del temp_start_frontend.bat 2>nul

pause
