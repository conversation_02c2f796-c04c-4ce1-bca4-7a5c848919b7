import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Brain, 
  Shield, 
  Zap, 
  BarChart3, 
  Users,
  Star,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const Home = () => {
  const features = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "IA Avanzada",
      description: "Análisis con GPT-4 Vision y estrategia Germayori exclusiva"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Multi-Timeframe",
      description: "Análisis completo en D1, H4, H1 y M5 para máxima precisión"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Gestión de Riesgo",
      description: "Stop Loss y Take Profit calculados automáticamente"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "<PERSON><PERSON><PERSON>",
      description: "Resultados en 30-60 segundos con alta precisión"
    }
  ]

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Trader Profesional",
      content: "La Legendaria Germayori cambió mi forma de hacer trading. Las señales son increíblemente precisas.",
      rating: 5
    },
    {
      name: "María <PERSON>",
      role: "Inversionista",
      content: "El análisis multi-timeframe me ha ayudado a tomar mejores decisiones de inversión.",
      rating: 5
    },
    {
      name: "Roberto Silva",
      role: "Analista Financiero",
      content: "La estrategia Germayori implementada en la IA es simplemente revolucionaria.",
      rating: 5
    }
  ]

  const plans = [
    {
      name: "Gratuito",
      price: "0",
      period: "siempre",
      features: [
        "5 análisis por mes",
        "Análisis básico con IA",
        "Soporte por email"
      ],
      cta: "Comenzar Gratis",
      popular: false
    },
    {
      name: "Premium",
      price: "29.99",
      period: "mes",
      features: [
        "100 análisis por mes",
        "Análisis avanzado con IA",
        "Soporte prioritario",
        "Reportes detallados"
      ],
      cta: "Elegir Premium",
      popular: true
    },
    {
      name: "VIP",
      price: "99.99",
      period: "mes",
      features: [
        "Análisis ilimitados",
        "Estrategia Germayori exclusiva",
        "Soporte 24/7",
        "Comunidad VIP",
        "Señales en tiempo real"
      ],
      cta: "Ser VIP",
      popular: false
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-primary text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-hero-pattern opacity-10"></div>
        <div className="container mx-auto px-6 relative z-10">
          <motion.div 
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="gold-text">La Legendaria</span>
              <br />
              <span className="text-white">Germayori</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200">
              Análisis financiero revolucionario con Inteligencia Artificial
            </p>
            <p className="text-lg mb-10 text-gray-300 max-w-2xl mx-auto">
              Obtén señales de trading precisas, análisis técnico profesional y gestión de riesgo automática 
              con nuestra estrategia Germayori potenciada por GPT-4 Vision.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn-gold text-lg px-8 py-4">
                Comenzar Gratis
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <Link to="/plans" className="btn-secondary text-lg px-8 py-4">
                Ver Planes
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">
              ¿Por qué elegir La Legendaria Germayori?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Combinamos años de experiencia en trading con la tecnología más avanzada 
              para ofrecerte análisis financieros de nivel profesional.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="card text-center hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-blue-600 mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How it Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">
              Cómo Funciona
            </h2>
            <p className="text-xl text-gray-600">
              Obtén análisis profesionales en 3 simples pasos
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                step: "1",
                title: "Sube tus Gráficos",
                description: "Carga 4 imágenes de tus gráficos (D1, H4, H1, M5) de cualquier par de divisas o criptomoneda"
              },
              {
                step: "2", 
                title: "IA Analiza",
                description: "Nuestra IA con estrategia Germayori analiza patrones, tendencias y niveles clave en segundos"
              },
              {
                step: "3",
                title: "Recibe Señales",
                description: "Obtén señales claras BUY/SELL con Stop Loss, Take Profit y análisis detallado"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="w-16 h-16 bg-gradient-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-xl font-semibold mb-3">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">
              Lo que dicen nuestros usuarios
            </h2>
            <p className="text-xl text-gray-600">
              Miles de traders confían en La Legendaria Germayori
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="card"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">
              Planes para Todos
            </h2>
            <p className="text-xl text-gray-600">
              Elige el plan que mejor se adapte a tu estilo de trading
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={index}
                className={`card relative ${plan.popular ? 'ring-2 ring-blue-500 transform scale-105' : ''}`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-primary text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Más Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold">${plan.price}</span>
                    <span className="text-gray-600">/{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link 
                  to="/register" 
                  className={`w-full text-center block py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                    plan.popular 
                      ? 'btn-primary' 
                      : 'btn-secondary'
                  }`}
                >
                  {plan.cta}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-4">
              ¿Listo para revolucionar tu trading?
            </h2>
            <p className="text-xl mb-8 text-gray-200 max-w-2xl mx-auto">
              Únete a miles de traders que ya están usando La Legendaria Germayori 
              para obtener análisis financieros de nivel profesional.
            </p>
            <Link to="/register" className="btn-gold text-lg px-8 py-4">
              Comenzar Ahora Gratis
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Home
