// models/Payment.js - Modelo de Pagos para La Legendaria Germayori
const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  // Referencia al usuario
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'El ID del usuario es requerido']
  },
  
  // Información del pago
  paymentId: {
    type: String,
    unique: true,
    required: true
  },
  
  // Datos de Stripe
  stripePaymentIntentId: String,
  stripeChargeId: String,
  stripeInvoiceId: String,
  stripeSubscriptionId: String,
  stripeCustomerId: String,
  
  // Información del producto/servicio
  planType: {
    type: String,
    enum: ['premium_monthly', 'premium_yearly', 'vip_monthly', 'vip_yearly', 'one_time_analysis'],
    required: true
  },
  
  planDetails: {
    name: String,
    description: String,
    features: [String],
    analysisLimit: Number, // -1 para ilimitado
    duration: Number, // en días
    price: {
      amount: Number,
      currency: { type: String, default: 'USD' }
    }
  },
  
  // Estado del pago
  status: {
    type: String,
    enum: ['pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded'],
    default: 'pending'
  },
  
  // Montos
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true
  },
  
  // Descuentos y promociones
  discount: {
    code: String,
    type: { type: String, enum: ['percentage', 'fixed'] },
    value: Number,
    appliedAmount: Number
  },
  
  // Información de facturación
  billingInfo: {
    email: String,
    name: String,
    address: {
      line1: String,
      line2: String,
      city: String,
      state: String,
      postal_code: String,
      country: String
    },
    phone: String
  },
  
  // Método de pago
  paymentMethod: {
    type: {
      type: String,
      enum: ['card', 'bank_transfer', 'paypal', 'crypto'],
      default: 'card'
    },
    last4: String, // Últimos 4 dígitos de la tarjeta
    brand: String, // visa, mastercard, etc.
    country: String,
    fingerprint: String
  },
  
  // Fechas importantes
  paidAt: Date,
  refundedAt: Date,
  subscriptionStartDate: Date,
  subscriptionEndDate: Date,
  
  // Información de la suscripción
  subscription: {
    isRecurring: { type: Boolean, default: false },
    interval: { type: String, enum: ['month', 'year'] },
    intervalCount: { type: Number, default: 1 },
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: { type: Boolean, default: false },
    canceledAt: Date,
    trialStart: Date,
    trialEnd: Date
  },
  
  // Reembolsos
  refunds: [{
    refundId: String,
    amount: Number,
    reason: String,
    status: String,
    processedAt: Date,
    stripeRefundId: String
  }],
  
  // Facturas
  invoice: {
    number: String,
    url: String,
    pdfUrl: String,
    hostedUrl: String
  },
  
  // Metadata adicional
  metadata: {
    source: String, // web, mobile, admin
    campaign: String,
    referrer: String,
    userAgent: String,
    ipAddress: String
  },
  
  // Información de procesamiento
  processing: {
    gateway: { type: String, default: 'stripe' },
    gatewayTransactionId: String,
    processingFee: Number,
    netAmount: Number,
    exchangeRate: Number
  },
  
  // Notificaciones
  notifications: {
    emailSent: { type: Boolean, default: false },
    emailSentAt: Date,
    webhookProcessed: { type: Boolean, default: false },
    webhookProcessedAt: Date
  },
  
  // Errores y logs
  errors: [{
    message: String,
    code: String,
    timestamp: { type: Date, default: Date.now },
    details: mongoose.Schema.Types.Mixed
  }],
  
  // Flags
  isTest: { type: Boolean, default: false },
  isDisputed: { type: Boolean, default: false },
  isArchived: { type: Boolean, default: false },
  
  // Notas internas
  internalNotes: String,
  
  // Información de renovación
  renewal: {
    willRenew: { type: Boolean, default: true },
    nextBillingDate: Date,
    priceAtRenewal: Number,
    reminderSent: { type: Boolean, default: false }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices para optimizar consultas
paymentSchema.index({ userId: 1, createdAt: -1 });
paymentSchema.index({ paymentId: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ planType: 1 });
paymentSchema.index({ stripePaymentIntentId: 1 });
paymentSchema.index({ stripeSubscriptionId: 1 });
paymentSchema.index({ 'subscription.currentPeriodEnd': 1 });

// Virtual para verificar si el pago está activo
paymentSchema.virtual('isActive').get(function() {
  if (this.status !== 'succeeded') return false;
  if (!this.subscription.isRecurring) return false;
  if (this.subscription.cancelAtPeriodEnd) return false;
  return this.subscription.currentPeriodEnd > new Date();
});

// Virtual para calcular días restantes
paymentSchema.virtual('daysRemaining').get(function() {
  if (!this.subscriptionEndDate) return 0;
  const now = new Date();
  const end = new Date(this.subscriptionEndDate);
  const diffTime = end - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Método para generar ID único de pago
paymentSchema.pre('save', function(next) {
  if (!this.paymentId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.paymentId = `PAY-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Método para marcar como pagado
paymentSchema.methods.markAsPaid = async function(stripeData) {
  this.status = 'succeeded';
  this.paidAt = new Date();
  
  if (stripeData) {
    this.stripePaymentIntentId = stripeData.paymentIntentId;
    this.stripeChargeId = stripeData.chargeId;
    this.processing.gatewayTransactionId = stripeData.transactionId;
  }
  
  return await this.save();
};

// Método para procesar reembolso
paymentSchema.methods.processRefund = async function(amount, reason) {
  const refund = {
    refundId: `REF-${Date.now().toString(36)}-${Math.random().toString(36).substr(2, 5)}`.toUpperCase(),
    amount: amount || this.amount,
    reason: reason || 'Requested by customer',
    status: 'pending',
    processedAt: new Date()
  };
  
  this.refunds.push(refund);
  
  if (amount >= this.amount) {
    this.status = 'refunded';
    this.refundedAt = new Date();
  }
  
  return await this.save();
};

// Método para cancelar suscripción
paymentSchema.methods.cancelSubscription = async function(immediately = false) {
  if (immediately) {
    this.subscription.canceledAt = new Date();
    this.subscription.currentPeriodEnd = new Date();
  } else {
    this.subscription.cancelAtPeriodEnd = true;
  }
  
  return await this.save();
};

// Método estático para obtener ingresos por período
paymentSchema.statics.getRevenueStats = async function(startDate, endDate) {
  const stats = await this.aggregate([
    {
      $match: {
        status: 'succeeded',
        paidAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$amount' },
        totalTransactions: { $sum: 1 },
        avgTransactionValue: { $avg: '$amount' },
        planBreakdown: {
          $push: {
            planType: '$planType',
            amount: '$amount'
          }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalRevenue: 0,
    totalTransactions: 0,
    avgTransactionValue: 0,
    planBreakdown: []
  };
};

// Método estático para obtener suscripciones activas
paymentSchema.statics.getActiveSubscriptions = async function() {
  return await this.find({
    status: 'succeeded',
    'subscription.isRecurring': true,
    'subscription.currentPeriodEnd': { $gt: new Date() },
    'subscription.cancelAtPeriodEnd': false
  }).populate('userId', 'nombre apellido email');
};

module.exports = mongoose.model('Payment', paymentSchema);
