// routes/analysis.js - Rutas de Análisis para La Legendaria Germayori
const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Analysis = require('../models/Analysis');
const { auth, checkAnalysisLimit } = require('../middleware/auth');
const { analyzeChartsWithAI, validateImages } = require('../services/openai');

const router = express.Router();

// Configuración de Multer para subida de imágenes
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `chart-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB por archivo
    files: 4 // Máximo 4 archivos
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Solo se permiten imágenes (JPEG, PNG, GIF, WebP)'));
    }
  }
});

// @route   POST /api/analysis/upload
// @desc    Subir imágenes y realizar análisis con IA
// @access  Private (requiere límite de análisis)
router.post('/upload', auth, checkAnalysisLimit, upload.array('charts', 4), [
  body('pair').optional().isString().withMessage('Par de divisas inválido'),
  body('market').optional().isIn(['forex', 'crypto', 'stocks']).withMessage('Mercado inválido'),
  body('config.riskLevel').optional().isIn(['conservative', 'moderate', 'aggressive']).withMessage('Nivel de riesgo inválido'),
  body('config.timeHorizon').optional().isIn(['scalping', 'day', 'swing', 'position']).withMessage('Horizonte temporal inválido')
], async (req, res) => {
  try {
    // Verificar errores de validación
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Limpiar archivos subidos en caso de error
      if (req.files) {
        req.files.forEach(file => {
          fs.unlink(file.path, () => {});
        });
      }
      
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    // Verificar que se subieron exactamente 4 imágenes
    if (!req.files || req.files.length !== 4) {
      // Limpiar archivos subidos
      if (req.files) {
        req.files.forEach(file => {
          fs.unlink(file.path, () => {});
        });
      }
      
      return res.status(400).json({
        success: false,
        message: 'Debes subir exactamente 4 imágenes (D1, H4, H1, M5)',
        received: req.files ? req.files.length : 0
      });
    }

    const { pair, market, config } = req.body;
    const imagePaths = req.files.map(file => file.path);

    // Validar imágenes
    const imageErrors = validateImages(imagePaths);
    if (imageErrors.length > 0) {
      // Limpiar archivos subidos
      req.files.forEach(file => {
        fs.unlink(file.path, () => {});
      });
      
      return res.status(400).json({
        success: false,
        message: 'Errores en las imágenes',
        errors: imageErrors
      });
    }

    // Crear registro de análisis
    const analysis = new Analysis({
      userId: req.user.userId,
      images: {
        d1: {
          filename: req.files[0].filename,
          path: req.files[0].path,
          originalName: req.files[0].originalname,
          size: req.files[0].size
        },
        h4: {
          filename: req.files[1].filename,
          path: req.files[1].path,
          originalName: req.files[1].originalname,
          size: req.files[1].size
        },
        h1: {
          filename: req.files[2].filename,
          path: req.files[2].path,
          originalName: req.files[2].originalname,
          size: req.files[2].size
        },
        m5: {
          filename: req.files[3].filename,
          path: req.files[3].path,
          originalName: req.files[3].originalname,
          size: req.files[3].size
        }
      },
      pair: pair || 'Unknown',
      market: market || 'forex',
      analysisConfig: {
        riskLevel: config?.riskLevel || 'moderate',
        timeHorizon: config?.timeHorizon || 'day',
        includeVolume: config?.includeVolume !== false,
        includeFundamentals: config?.includeFundamentals === true
      },
      status: 'processing'
    });

    await analysis.save();

    // Responder inmediatamente con el ID del análisis
    res.status(202).json({
      success: true,
      message: 'Imágenes recibidas. Iniciando análisis con IA...',
      data: {
        analysisId: analysis.analysisId,
        status: 'processing',
        estimatedTime: '30-60 segundos',
        images: req.files.map((file, index) => ({
          timeframe: ['D1', 'H4', 'H1', 'M5'][index],
          filename: file.filename,
          size: file.size
        }))
      }
    });

    // Procesar análisis en background
    processAnalysisInBackground(analysis, imagePaths, req.user.userId);

  } catch (error) {
    console.error('Error en upload de análisis:', error);
    
    // Limpiar archivos en caso de error
    if (req.files) {
      req.files.forEach(file => {
        fs.unlink(file.path, () => {});
      });
    }

    res.status(500).json({
      success: false,
      message: 'Error procesando análisis',
      error: error.message
    });
  }
});

// Función para procesar análisis en background
async function processAnalysisInBackground(analysis, imagePaths, userId) {
  const startTime = Date.now();
  
  try {
    console.log(`🔄 Iniciando análisis ${analysis.analysisId} para usuario ${userId}`);
    
    // Realizar análisis con OpenAI
    const aiResult = await analyzeChartsWithAI(imagePaths, analysis.analysisConfig);
    
    if (aiResult.success) {
      // Marcar análisis como completado
      await analysis.markCompleted(aiResult.analysis);
      
      // Actualizar contador del usuario
      const user = await User.findById(userId);
      if (user) {
        await user.incrementAnalysisCount();
      }
      
      console.log(`✅ Análisis ${analysis.analysisId} completado exitosamente`);
      
    } else {
      throw new Error('Error en análisis de IA');
    }
    
  } catch (error) {
    console.error(`❌ Error procesando análisis ${analysis.analysisId}:`, error);
    
    // Marcar análisis como fallido
    await analysis.markFailed(error);
  } finally {
    // Establecer tiempo de procesamiento
    analysis.setProcessingTime(startTime);
    await analysis.save();
  }
}

// @route   GET /api/analysis/:analysisId
// @desc    Obtener resultado de análisis específico
// @access  Private
router.get('/:analysisId', auth, async (req, res) => {
  try {
    const { analysisId } = req.params;

    const analysis = await Analysis.findOne({ 
      analysisId: analysisId,
      userId: req.user.userId 
    });

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análisis no encontrado'
      });
    }

    // Incrementar vistas si el análisis está completado
    if (analysis.status === 'completed') {
      await analysis.incrementViews();
    }

    res.json({
      success: true,
      data: {
        analysis: {
          id: analysis.analysisId,
          status: analysis.status,
          pair: analysis.pair,
          market: analysis.market,
          createdAt: analysis.createdAt,
          processingTime: analysis.processingTime,
          views: analysis.views,
          config: analysis.analysisConfig,
          images: {
            d1: analysis.images.d1.filename,
            h4: analysis.images.h4.filename,
            h1: analysis.images.h1.filename,
            m5: analysis.images.m5.filename
          },
          result: analysis.status === 'completed' ? analysis.aiAnalysis : null,
          errors: analysis.errors
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo análisis:', error);
    res.status(500).json({
      success: false,
      message: 'Error obteniendo análisis',
      error: error.message
    });
  }
});

// @route   GET /api/analysis
// @desc    Obtener historial de análisis del usuario
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const skip = (page - 1) * limit;

    // Construir filtros
    const filters = { userId: req.user.userId };
    if (status && ['pending', 'processing', 'completed', 'failed'].includes(status)) {
      filters.status = status;
    }

    const analyses = await Analysis.find(filters)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-images.d1.path -images.h4.path -images.h1.path -images.m5.path');

    const total = await Analysis.countDocuments(filters);

    res.json({
      success: true,
      message: 'Historial de análisis obtenido exitosamente',
      data: {
        analyses: analyses.map(analysis => ({
          id: analysis.analysisId,
          status: analysis.status,
          pair: analysis.pair,
          market: analysis.market,
          createdAt: analysis.createdAt,
          processingTime: analysis.processingTime,
          views: analysis.views,
          signal: analysis.status === 'completed' ? analysis.aiAnalysis.signal : null
        })),
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        },
        stats: {
          total: total,
          completed: await Analysis.countDocuments({ ...filters, status: 'completed' }),
          processing: await Analysis.countDocuments({ ...filters, status: 'processing' }),
          failed: await Analysis.countDocuments({ ...filters, status: 'failed' })
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo historial:', error);
    res.status(500).json({
      success: false,
      message: 'Error obteniendo historial de análisis',
      error: error.message
    });
  }
});

// @route   POST /api/analysis/:analysisId/feedback
// @desc    Enviar feedback sobre un análisis
// @access  Private
router.post('/:analysisId/feedback', auth, [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating debe ser entre 1 y 5'),
  body('comment').optional().isString().withMessage('Comentario inválido'),
  body('wasAccurate').isBoolean().withMessage('wasAccurate debe ser boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Datos inválidos',
        errors: errors.array()
      });
    }

    const { analysisId } = req.params;
    const { rating, comment, wasAccurate } = req.body;

    const analysis = await Analysis.findOne({ 
      analysisId: analysisId,
      userId: req.user.userId 
    });

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análisis no encontrado'
      });
    }

    if (analysis.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Solo se puede dar feedback a análisis completados'
      });
    }

    // Actualizar feedback
    analysis.userFeedback = {
      rating,
      comment: comment || '',
      wasAccurate,
      submittedAt: new Date()
    };

    await analysis.save();

    res.json({
      success: true,
      message: 'Feedback enviado exitosamente',
      data: {
        feedback: analysis.userFeedback
      }
    });

  } catch (error) {
    console.error('Error enviando feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Error enviando feedback',
      error: error.message
    });
  }
});

module.exports = router;
