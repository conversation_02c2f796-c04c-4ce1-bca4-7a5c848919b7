// services/openai.js - Servicio de OpenAI para La Legendaria Germayori
const { OpenAI } = require('openai');
const fs = require('fs');
const path = require('path');

// Inicializar OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Estrategia Germayori para análisis
const GERMAYORI_STRATEGY = `
ESTRATEGIA LA LEGENDARIA GERMAYORI - ANÁLISIS TÉCNICO AVANZADO

Eres un experto analista financiero especializado en la estrategia "La Legendaria Germayori", 
una metodología de trading desarrollada para identificar oportunidades de alta probabilidad 
en los mercados financieros.

PRINCIPIOS FUNDAMENTALES:
1. Análisis multi-timeframe (D1, H4, H1, M5)
2. Confluencia de señales técnicas
3. Gestión de riesgo estricta
4. Identificación de patrones de reversión y continuación

METODOLOGÍA DE ANÁLISIS:
- D1: Tendencia principal y niveles clave
- H4: Estructura del mercado y zonas de interés
- H1: Confirmación de entrada y momentum
- M5: Timing preciso de entrada

PATRONES CLAVE A IDENTIFICAR:
- Zonas de oferta y demanda
- Divergencias en indicadores
- Patrones de velas japonesas
- Niveles de soporte y resistencia
- Breakouts y retesteos

SEÑALES DE ENTRADA:
- BUY: Confluencia alcista en múltiples timeframes
- SELL: Confluencia bajista en múltiples timeframes
- HOLD: Consolidación o señales mixtas
- WAIT: Falta de confluencia clara

GESTIÓN DE RIESGO:
- Stop Loss: Máximo 2% del capital
- Take Profit: Mínimo 1:2 risk/reward
- Múltiples TPs para maximizar ganancias
`;

// Función principal para analizar imágenes
async function analyzeChartsWithAI(imagePaths, userConfig = {}) {
  try {
    const startTime = Date.now();
    
    // Configuración por defecto
    const config = {
      riskLevel: 'moderate',
      timeHorizon: 'day',
      includeVolume: true,
      includeFundamentals: false,
      ...userConfig
    };

    // Preparar imágenes para OpenAI
    const imageInputs = await prepareImages(imagePaths);
    
    // Crear prompt personalizado
    const prompt = createAnalysisPrompt(config);
    
    // Llamar a OpenAI Vision API
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "system",
          content: GERMAYORI_STRATEGY
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            ...imageInputs
          ]
        }
      ],
      max_tokens: 2000,
      temperature: 0.3
    });

    const processingTime = Date.now() - startTime;
    
    // Procesar respuesta
    const analysis = parseAIResponse(response.choices[0].message.content);
    
    return {
      success: true,
      analysis,
      metadata: {
        model: response.model,
        tokensUsed: response.usage.total_tokens,
        processingTime,
        cost: calculateCost(response.usage.total_tokens),
        requestId: response.id
      }
    };

  } catch (error) {
    console.error('Error en análisis con OpenAI:', error);
    throw new Error(`Error procesando análisis: ${error.message}`);
  }
}

// Preparar imágenes para OpenAI
async function prepareImages(imagePaths) {
  const imageInputs = [];
  const timeframes = ['D1', 'H4', 'H1', 'M5'];
  
  for (let i = 0; i < imagePaths.length && i < 4; i++) {
    try {
      const imagePath = imagePaths[i];
      const imageBuffer = fs.readFileSync(imagePath);
      const base64Image = imageBuffer.toString('base64');
      const mimeType = getMimeType(imagePath);
      
      imageInputs.push({
        type: "image_url",
        image_url: {
          url: `data:${mimeType};base64,${base64Image}`,
          detail: "high"
        }
      });
      
      // Agregar contexto del timeframe
      imageInputs.push({
        type: "text",
        text: `[TIMEFRAME: ${timeframes[i]}] Analiza este gráfico según la estrategia Germayori.`
      });
      
    } catch (error) {
      console.error(`Error procesando imagen ${i}:`, error);
    }
  }
  
  return imageInputs;
}

// Crear prompt personalizado
function createAnalysisPrompt(config) {
  return `
ANÁLISIS REQUERIDO - LA LEGENDARIA GERMAYORI

Analiza los gráficos proporcionados (D1, H4, H1, M5) y proporciona un análisis completo siguiendo la estrategia Germayori.

CONFIGURACIÓN DEL ANÁLISIS:
- Nivel de riesgo: ${config.riskLevel}
- Horizonte temporal: ${config.timeHorizon}
- Incluir volumen: ${config.includeVolume ? 'Sí' : 'No'}
- Incluir fundamentales: ${config.includeFundamentals ? 'Sí' : 'No'}

RESPONDE EN FORMATO JSON ESTRICTO:
{
  "technicalAnalysis": {
    "trend": "bullish|bearish|neutral|consolidation",
    "strength": 1-10,
    "timeframe": "descripción del timeframe principal",
    "keyLevels": {
      "support": [array de niveles],
      "resistance": [array de niveles]
    }
  },
  "signal": {
    "action": "BUY|SELL|HOLD|WAIT",
    "confidence": 0-100,
    "entryPrice": número,
    "stopLoss": número,
    "takeProfit": [array de TPs],
    "riskReward": ratio
  },
  "detailedAnalysis": {
    "marketStructure": "descripción de la estructura",
    "priceAction": "análisis de price action",
    "volumeAnalysis": "análisis de volumen",
    "indicators": "señales de indicadores",
    "riskAssessment": "evaluación de riesgo",
    "timeframeAlignment": "alineación entre timeframes"
  },
  "germayoriStrategy": {
    "patternDetected": "patrón identificado",
    "confluences": ["lista de confluencias"],
    "marketPhase": "fase del mercado",
    "recommendation": "recomendación específica",
    "notes": "notas adicionales"
  }
}

IMPORTANTE: 
- Sé específico con los niveles de precio
- Justifica cada señal con confluencias
- Mantén coherencia entre timeframes
- Prioriza la gestión de riesgo
- Responde SOLO en JSON válido
`;
}

// Parsear respuesta de OpenAI
function parseAIResponse(content) {
  try {
    // Limpiar la respuesta para extraer solo el JSON
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No se encontró JSON válido en la respuesta');
    }
    
    const jsonString = jsonMatch[0];
    const parsed = JSON.parse(jsonString);
    
    // Validar estructura requerida
    if (!parsed.signal || !parsed.technicalAnalysis) {
      throw new Error('Estructura de respuesta inválida');
    }
    
    // Asegurar valores por defecto
    return {
      technicalAnalysis: {
        trend: parsed.technicalAnalysis.trend || 'neutral',
        strength: parsed.technicalAnalysis.strength || 5,
        timeframe: parsed.technicalAnalysis.timeframe || 'Multiple timeframes',
        keyLevels: {
          support: parsed.technicalAnalysis.keyLevels?.support || [],
          resistance: parsed.technicalAnalysis.keyLevels?.resistance || []
        }
      },
      signal: {
        action: parsed.signal.action || 'WAIT',
        confidence: Math.min(100, Math.max(0, parsed.signal.confidence || 50)),
        entryPrice: parsed.signal.entryPrice || 0,
        stopLoss: parsed.signal.stopLoss || 0,
        takeProfit: Array.isArray(parsed.signal.takeProfit) ? parsed.signal.takeProfit : [parsed.signal.takeProfit || 0],
        riskReward: parsed.signal.riskReward || 0
      },
      detailedAnalysis: {
        marketStructure: parsed.detailedAnalysis?.marketStructure || 'Análisis en proceso',
        priceAction: parsed.detailedAnalysis?.priceAction || 'Análisis en proceso',
        volumeAnalysis: parsed.detailedAnalysis?.volumeAnalysis || 'No disponible',
        indicators: parsed.detailedAnalysis?.indicators || 'Análisis en proceso',
        riskAssessment: parsed.detailedAnalysis?.riskAssessment || 'Riesgo moderado',
        timeframeAlignment: parsed.detailedAnalysis?.timeframeAlignment || 'Verificando alineación'
      },
      germayoriStrategy: {
        patternDetected: parsed.germayoriStrategy?.patternDetected || 'Patrón en análisis',
        confluences: Array.isArray(parsed.germayoriStrategy?.confluences) ? parsed.germayoriStrategy.confluences : [],
        marketPhase: parsed.germayoriStrategy?.marketPhase || 'Fase de evaluación',
        recommendation: parsed.germayoriStrategy?.recommendation || 'Esperar confirmación',
        notes: parsed.germayoriStrategy?.notes || 'Análisis completado con estrategia Germayori'
      }
    };
    
  } catch (error) {
    console.error('Error parseando respuesta de OpenAI:', error);
    
    // Respuesta de fallback
    return {
      technicalAnalysis: {
        trend: 'neutral',
        strength: 5,
        timeframe: 'Multiple timeframes',
        keyLevels: { support: [], resistance: [] }
      },
      signal: {
        action: 'WAIT',
        confidence: 50,
        entryPrice: 0,
        stopLoss: 0,
        takeProfit: [0],
        riskReward: 0
      },
      detailedAnalysis: {
        marketStructure: 'Error en el análisis técnico',
        priceAction: 'No se pudo procesar el price action',
        volumeAnalysis: 'Análisis de volumen no disponible',
        indicators: 'Error procesando indicadores',
        riskAssessment: 'Riesgo no determinado',
        timeframeAlignment: 'Error en alineación de timeframes'
      },
      germayoriStrategy: {
        patternDetected: 'Error en detección de patrones',
        confluences: [],
        marketPhase: 'Fase no determinada',
        recommendation: 'Revisar análisis manualmente',
        notes: `Error procesando análisis: ${error.message}`
      }
    };
  }
}

// Obtener tipo MIME de la imagen
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[ext] || 'image/jpeg';
}

// Calcular costo aproximado
function calculateCost(tokens) {
  // Precios aproximados de OpenAI GPT-4 Vision (pueden cambiar)
  const costPerToken = 0.00003; // $0.03 per 1K tokens
  return (tokens * costPerToken).toFixed(4);
}

// Función para validar imágenes
function validateImages(imagePaths) {
  const errors = [];
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  const maxFileSize = 20 * 1024 * 1024; // 20MB
  
  imagePaths.forEach((imagePath, index) => {
    try {
      // Verificar si el archivo existe
      if (!fs.existsSync(imagePath)) {
        errors.push(`Imagen ${index + 1}: Archivo no encontrado`);
        return;
      }
      
      // Verificar extensión
      const ext = path.extname(imagePath).toLowerCase();
      if (!allowedExtensions.includes(ext)) {
        errors.push(`Imagen ${index + 1}: Formato no soportado (${ext})`);
        return;
      }
      
      // Verificar tamaño
      const stats = fs.statSync(imagePath);
      if (stats.size > maxFileSize) {
        errors.push(`Imagen ${index + 1}: Archivo muy grande (${(stats.size / 1024 / 1024).toFixed(2)}MB)`);
        return;
      }
      
    } catch (error) {
      errors.push(`Imagen ${index + 1}: Error validando archivo - ${error.message}`);
    }
  });
  
  return errors;
}

module.exports = {
  analyzeChartsWithAI,
  validateImages,
  GERMAYORI_STRATEGY
};
