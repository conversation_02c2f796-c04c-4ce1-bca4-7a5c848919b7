# La Legendaria Germayori - Variables de Entorno
NODE_ENV=development
PORT=5000

# Base de Datos MongoDB Atlas
MONGO_URI=mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI

# Autenticación JWT
JWT_SECRET=la-legendaria-germayori-super-secret-key-2024
JWT_EXPIRES_IN=7d

# OpenAI API (necesitas configurar tu API key)
OPENAI_API_KEY=sk-tu-openai-api-key-aqui
OPENAI_MODEL=gpt-4-vision-preview

# Stripe (necesitas configurar tus keys de Stripe)
STRIPE_SECRET_KEY=sk_test_tu-stripe-secret-key-aqui
STRIPE_PUBLISHABLE_KEY=pk_test_tu-stripe-publishable-key-aqui
STRIPE_WEBHOOK_SECRET=whsec_tu-stripe-webhook-secret-aqui

# URLs del Frontend
FRONTEND_URL=http://localhost:3000

# Configuración de Archivos
MAX_FILE_SIZE=20971520
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuración de Análisis
DEFAULT_ANALYSIS_TIMEOUT=60000
MAX_CONCURRENT_ANALYSIS=5

# Configuración de Desarrollo
DEBUG=true
VERBOSE_LOGGING=true
